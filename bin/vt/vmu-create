#!/usr/bin/env bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$(dirname "$0")/vm-utils"

# BOLD="\033[1m"
RESET="\033[0m"
BLUE="\033[1;34m"
GREEN="\033[1;32m"
YELLOW="\033[1;33m"
RED="\033[1;31m"

info() { echo -e "${BLUE}ℹ️  $1${RESET}"; }
success() { echo -e "${GREEN}✅ $1${RESET}"; }
warn() { echo -e "${YELLOW}⚠️  $1${RESET}"; }
error() { echo -e "${RED}❌ $1${RESET}"; }

safe_rm() {
  if has_cmd trash; then
    trash "$@" 2>/dev/null || true
  else
    rm -rf "$@" 2>/dev/null || true
  fi
}

usage() {
  cat <<EOF
  Usage: $0 --distro DISTRO [OPTIONS]

  Create a VM using virt-install and cloud-init for various Linux distributions.

  REQUIRED:
      --distro DISTRO     Distribution to install (alpine|debian|ubuntu|fedora|arch|tw)

  RECOMMENDED:
      --ssh-port PORT     SSH port (default: random)
      --name NAME         VM name (default: distribution name)
      --username USER     VM username (default: distribution-specific)

  OPTIONS:
      --vcpus NUM         Number of vCPUs (default: 4)
      --memory MB         RAM in MB (default: 8192)
      --disk-size SIZE    Disk size (default: 20G)
      --ssh-key PATH      SSH public key path (default: auto-detect)
      --password PASS     User password (default: username)
      --ask-password      Prompt for password
      --console           Attach console to VM
      --spice             Attach spice to VM
      --help, -h          Show this help
  EXAMPLES:
      $0 --distro alpine
      $0 --distro debian --name my-debian --username admin --password mypass
      $0 --distro ubuntu --vcpus 4 --memory 4096 --disk-size 40G
      $0 --distro fedora --ssh-key ~/.ssh/my_key.pub
      $0 --distro arch --console --spice
      $0 --distro tw --ask-password
EOF
}

has_cmd() {
  command -v "$1" &>/dev/null
}

parse_args() {
  ASK_PASSWORD=false
  CPUS=4
  RAM_MB=8192
  DISK_SIZE=20G
  CONSOLE=false
  SPICE=false

  while [[ $# -gt 0 ]]; do
    case $1 in
    --ask-password)
      ASK_PASSWORD=true
      shift
      ;;
    --distro)
      DISTRO="$2"
      shift 2
      ;;
    --name)
      VM_NAME="$2"
      shift 2
      ;;
    --vcpus)
      CPUS="$2"
      shift 2
      ;;
    --memory)
      RAM_MB="$2"
      shift 2
      ;;
    --disk-size)
      DISK_SIZE="$2"
      shift 2
      ;;
    --ssh-key)
      SSH_KEY="$2"
      shift 2
      ;;
    --username)
      USER_NAME="$2"
      shift 2
      ;;
    --password)
      PASSWORD="$2"
      shift 2
      ;;
    --ssh-port)
      SSH_PORT="$2"
      shift 2
      ;;
    --console)
      CONSOLE=true
      shift
      ;;
    --spice)
      SPICE=true
      shift
      ;;
    --help | -h)
      usage
      exit 0
      ;;
    *)
      error "Unknown option: $1"
      usage
      exit 1
      ;;
    esac
  done

  if [[ -z "$DISTRO" ]]; then
    error "Distro is required"
    usage
    exit 1
  fi

  if [[ -z "$SSH_PORT" ]]; then
    SSH_PORT=$(find_free_port_random)
    warn "SSH port not provided, using random: $SSH_PORT"
  fi

  VM_NAME=${VM_NAME:-$DISTRO-vmu}

  if [[ "$ASK_PASSWORD" == true ]]; then
    read -r -s -p "Enter password for $USER_NAME: " PASSWORD
    echo
  fi

  USER_NAME="${USER_NAME:-$DISTRO}"
  PASSWORD="${PASSWORD:-$USER_NAME}"

  BASE_DIR="${BASE_DIR:=$HOME/.vmus}"
  WORKDIR="$BASE_DIR/${VM_NAME}"
  BASE_IMG_DIR="$BASE_DIR/images/${DISTRO}"
  DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
}

create_dirs() {
  safe_rm "$WORKDIR"

  info "Creating directories: $BASE_IMG_DIR, $WORKDIR"
  mkdir -p "$BASE_IMG_DIR" "$WORKDIR"

  tree "$BASE_DIR"
  tree "$BASE_IMG_DIR"
  tree "$WORKDIR"
}

check_prerequisites() {
  local missing=()
  for cmd in qemu-img virt-install virsh jq openssl wget trash ssh-keygen wget; do
    has_cmd "$cmd" || missing+=("$cmd")
  done

  if ((${#missing[@]})); then
    echo -e "\n\033[1;31m❌ Missing required commands\033[0m\n"
    echo -e "\033[1;34m📦 Missing:\033[0m ${missing[*]}"
    echo -e "\033[1;34m💡 Install them before running this script.\033[0m\n"
    exit 1
  fi

  if virsh --connect qemu:///session list --all --name 2>/dev/null | grep -q "^$VM_NAME$"; then
    error "VM '$VM_NAME' already exists"
    exit 1
  fi

  if [[ -f ~/.ssh/id_ed25519.pub ]]; then
    SSH_KEY=$(cat "${HOME}/.ssh/id_ed25519.pub")
  else
    SSH_KEY=$(ssh-keygen -t ed25519 -f "${HOME}/.ssh/id_ed25519" -N "")
  fi
}

configure_distro() {
  case "$DISTRO" in
  alpine)
    IMG_URL="https://dl-cdn.alpinelinux.org/alpine/v3.22/releases/cloud/generic_alpine-3.22.1-x86_64-bios-cloudinit-r0.qcow2"
    BASE_IMG="${BASE_IMG_DIR}/generic_alpine-3.22.1-x86_64-bios-cloudinit-r0.qcow2"
    OS_VARIANT="alpinelinux3.21"
    ;;
  debian)
    IMG_URL="https://cloud.debian.org/images/cloud/trixie/latest/debian-13-generic-amd64.qcow2"
    BASE_IMG="${BASE_IMG_DIR}/debian-13-generic-amd64.qcow2"
    OS_VARIANT="debian13"
    ;;
  ubuntu)
    IMG_URL="https://cloud-images.ubuntu.com/plucky/current/plucky-server-cloudimg-amd64.img"
    BASE_IMG="${BASE_IMG_DIR}/plucky-server-cloudimg-amd64.img"
    OS_VARIANT="ubuntu25.04"
    ;;
  fedora)
    IMG_URL="https://download.fedoraproject.org/pub/fedora/linux/releases/42/Cloud/x86_64/images/Fedora-Cloud-Base-Generic-42-1.1.x86_64.qcow2"
    BASE_IMG="${BASE_IMG_DIR}/Fedora-Cloud-Base-Generic-42-1.1.x86_64.qcow2"
    OS_VARIANT="fedora42"
    ;;
  arch)
    IMG_URL="https://geo.mirror.pkgbuild.com/images/latest/Arch-Linux-x86_64-cloudimg.qcow2"
    BASE_IMG="${BASE_IMG_DIR}/Arch-Linux-x86_64-cloudimg.qcow2"
    OS_VARIANT="archlinux"
    ;;
  tw)
    IMG_URL="https://download.opensuse.org/tumbleweed/appliances/openSUSE-Tumbleweed-Minimal-VM.x86_64-Cloud.qcow2"
    BASE_IMG="${BASE_IMG_DIR}/openSUSE-Tumbleweed-Minimal-VM.x86_64-Cloud.qcow2"
    OS_VARIANT="opensusetumbleweed"
    ;;
  *)
    error "Unsupported distro: $DISTRO"
    usage
    ;;
  esac
}

cleanup_on_error() {
  info "An error occurred. Cleaning up..."
  safe_rm "$DISK_IMG"
}

trap cleanup_on_error ERR INT TERM

download_image() {
  if [[ -f "$BASE_IMG" ]]; then
    info "Using existing image: $BASE_IMG"
    sleep 1
  else
    info "Downloading image..."
    wget --quiet --show-progress -O "$BASE_IMG" "$IMG_URL"
  fi
}

create_disk() {
  info "Creating VM disk..."
  local ext
  ext=$(qemu-img info --output=json "$BASE_IMG" | jq -r '.format')
  qemu-img create -f qcow2 -F "$ext" -b "$BASE_IMG" "$DISK_IMG" "${DISK_SIZE}"
}

generate_cloud_init() {
  local password_hash cloud_init_dir
  password_hash=$(openssl passwd -6 "$PASSWORD")

  cloud_init_dir=$(mktemp -d)

  USER_DATA="${cloud_init_dir}/user-data"
  META_DATA="${cloud_init_dir}/meta-data"

  # This is deliberately insecure configuration as the vm is supposed to be for develpoment.
  cat >"$USER_DATA" <<EOF
#cloud-config
hostname: $VM_NAME

users:
  - name: $USER_NAME
    groups: [wheel, sudo]
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: $password_hash
    ssh_authorized_keys:
      - $SSH_KEY

ssh_pwauth: true
disable_root: false

package_update: true
package_upgrade: true

packages:
  - qemu-guest-agent
  - curl
  - bash

runcmd:
  - |
    if command -v systemctl >/dev/null 2>&1; then
        systemctl enable --now qemu-guest-agent || true
        systemctl enable --now sshd || systemctl enable --now ssh || true
    else
        rc-update add qemu-guest-agent default || true
        rc-update add sshd default || true
    fi
EOF

  cat >"$META_DATA" <<EOF
instance-id: $VM_NAME-$(date +%s)
local-hostname: $VM_NAME
EOF
}

create_vm() {
  info "Creating VM..."

  local -a virt_install_args
  virt_install_args=(
    --connect qemu:///session
    --name "$VM_NAME"
    --memory "$RAM_MB"
    --vcpus "$CPUS"
    --disk "path=$DISK_IMG,format=qcow2,bus=virtio"
    --cloud-init "user-data=$USER_DATA,meta-data=$META_DATA"
    --os-variant "$OS_VARIANT"
    --virt-type kvm
    --import
    --network "passt,model=virtio,portForward=${SSH_PORT}:22"
  )

  if [[ "$CONSOLE" == true ]]; then
    virt_install_args+=("--console pty,target_type=serial")
  else
    virt_install_args+=("--noautoconsole")
  fi

  if [[ "$SPICE" == true ]]; then
    virt_install_args+=("--graphics spice,gl=on,listen=none")
  fi

  echo "virt-install ${virt_install_args[*]}"
  echo

  echo "Command: virt-install ${virt_install_args[*]}"

  virt-install "${virt_install_args[@]}"
}

show_completion_info() {
  echo -e "\n\033[1;32m✅ VM Creation Complete\033[0m\n"

  echo -e "\033[1;34m📦 VM Details:\033[0m"
  echo -e "  🖥️  Name:        \033[1m$VM_NAME\033[0m"
  echo -e "  🐧 Distro:      \033[1m$DISTRO\033[0m"
  echo -e "  🧠 Memory:      ${RAM_MB}MB"
  echo -e "  ⚙️  vCPUs:       $CPUS"
  echo -e "  💾 Disk:        $DISK_IMG"
  echo -e "  👤 User_name:    \033[1m$USER_NAME\033[0m"
  echo -e "  🔑 Password:    \033[1m$PASSWORD\033[0m\n"

  echo -e "\033[1;34m🔌 Connect:\033[0m"
  echo "  virsh --connect qemu:///session console $VM_NAME"

  echo -e "\n\033[1;33m💡 Tip:\033[0m You can SSH once you know the IP:"
  echo -e "🔑  SSH into your VM with:\n  ssh -p ${SSH_PORT} ${USER_NAME}@localhost"
}

echo_all_vars() {
  echo "VM_NAME: $VM_NAME"
  echo "DISTRO: $DISTRO"
  echo "BASE_IMG: $BASE_IMG"
  echo "DISK_IMG: $DISK_IMG"
  echo "USER_DATA: $USER_DATA"
  echo "META_DATA: $META_DATA"
  echo "SSH_KEY: $SSH_KEY"
  echo "USER_NAME: $USER_NAME"
  echo "PASSWORD: $PASSWORD"
  echo "SSH_PORT: $SSH_PORT"
  echo "CPUS: $CPUS"
  echo "RAM_MB: $RAM_MB"
  echo "DISK_SIZE: $DISK_SIZE"
  echo "OS_VARIANT: $OS_VARIANT"
  sleep 1
}

main() {
  parse_args "$@"
  check_prerequisites
  configure_distro

  create_dirs
  download_image
  create_disk
  generate_cloud_init

  # print all info for debugging
  # echo_all_vars

  create_vm

  register_vm_port "$VM_NAME" "$SSH_PORT" "$USER_NAME"
  show_completion_info
}

main "$@"
