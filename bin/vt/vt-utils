#!/usr/bin/env bash

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

SUDO_KEEPALIVE_PID=""

start_sudo_keepalive() {
  slog "Starting sudo keepalive process..."
  sudo -v
  (while true; do
    sudo -v
    sleep 50
  done) &
  SUDO_KEEPALIVE_PID=$!
}

stop_sudo_keepalive() {
  if [ -n "$SUDO_KEEPALIVE_PID" ] && kill -0 "$SUDO_KEEPALIVE_PID" 2>/dev/null; then
    slog "Stopping sudo keepalive process..."
    kill "$SUDO_KEEPALIVE_PID" 2>/dev/null
  fi
  SUDO_KEEPALIVE_PID=""
}

is_net_service_running() {
  if ! has_cmd ss; then
    fail "ss command not found. Please install it first."
    return 1
  fi

  local service=${1:-ssh}
  sudo ss -tlnp | grep -q "$service"
}

define_default_net() {
  local net_name=${1:-default}
  if virsh net-info "$net_name" >/dev/null 2>&1; then
    echo "Network '$net_name' is already defined."
    return 0
  else
    echo "Network '$net_name' is not defined. Attempting to define it..."
  fi

  local candidates=(
    /usr/share/libvirt/networks/default.xml
    /usr/local/share/libvirt/networks/default.xml
    /var/lib/libvirt/network/default.xml
    /etc/libvirt/qemu/networks/default.xml
  )

  local xml
  for xml in "${candidates[@]}"; do
    if [[ -r "$xml" ]]; then
      echo "Found default network XML at $xml"
      virsh net-define "$xml"
      return $?
    fi
  done

  echo "Dumping default network XML to /tmp/default.xml to define it..."
  rm -f /tmp/default.xml
  virsh net-dumpxml default >/tmp/default.xml
  virsh net-define /tmp/default.xml
}

undefine_default_net() {
  local net_name=${1:-default}
  if virsh net-info "$net_name" >/dev/null 2>&1; then
    echo "Network '$net_name' is defined. Attempting to undefine it..."
    virsh net-destroy "$net_name"
    virsh net-undefine "$net_name"
  else
    echo "Network '$net_name' is not defined."
    return 0
  fi
}

ensure_libvirt_default_net() {
  local net_name=${1:default}
  define_default_net "$net_name" || {
    echo "Failed to define network '$net_name'" >&2
    return 1
  }

  local state
  state=$(virsh net-info "$net_name" | awk '/State:/ {print $2}')
  if [[ "$state" != "active" ]]; then
    echo "Starting network '$net_name'..."
    virsh net-start "$net_name" || return 1
  else
    echo "Network '$net_name' is already running."
  fi

  local autostart
  autostart=$(virsh net-info "$net_name" | awk '/Autostart:/ {print $2}')
  if [[ "$autostart" != "yes" ]]; then
    echo "Enabling autostart for '$net_name'..."
    virsh net-autostart "$net_name"
  fi
}

check_tmux() {
  if ! has_cmd tmux; then
    fail "tmux is not installed. Please install it first."
    exit 1
  fi
}

incus_instance_exists() {
  incus list type=container,virtual-machine --format csv --columns n | grep -q "^$1$"
}

default_username() {
  local distro="$1"

  local username

  case "$distro" in
  ubuntu*) username="ubuntu" ;;
  fedora*) username="fedora" ;;
  centos*) username="centos" ;;
  debian*) username="debian" ;;
  arch*) username="arch" ;;
  alpine*) username="alpine" ;;
  nix*) username="nixos" ;;
  rocky*) username="rocky" ;;
  tumbleweed* | tw*) username="opensuse" ;;
  *)
    username="$USER"
    ;;
  esac

  echo "$username"
}

incus_check() {
  if ! has_cmd incus; then
    fail "incus command not found. Please install Incus first."
    exit 1
  fi

  if ! incus info >/dev/null 2>&1; then
    fail "Cannot connect to Incus daemon. Please ensure Incus is running and you have proper permissions."
    exit 1
  fi
}

VMU_PORTS_FILE="$HOME/.vmu-ports"

find_free_port_random() {
  local max_attempts=100
  local vm_assigned_ports

  if [[ -f "$VMU_PORTS_FILE" ]]; then
    vm_assigned_ports=$(awk '{print $2}' "$VMU_PORTS_FILE")
  fi

  for ((attempt = 1; attempt <= max_attempts; attempt++)); do
    local port=$((RANDOM % (65000 - 2222 + 1) + 2222))

    if ss -l -t -n | awk '{print $4}' | grep -q ":$port\$"; then
      continue
    fi

    if [[ -n "$vm_assigned_ports" ]] && grep -q "^$port\$" <<<"$vm_assigned_ports"; then
      continue
    fi

    echo "$port"
    return 0
  done

  echo "Could not find free port after $max_attempts attempts" >&2
  return 1
}

register_vm_port() {
  local name="$1"
  local port="$2"
  local user="$3"

  # remove old entry if exists
  sed -i "/^$name /d" "$VMU_PORTS_FILE" 2>/dev/null || true
  touch "$VMU_PORTS_FILE"
  echo "$name $port $user" >>"$VMU_PORTS_FILE"
}

add_ssh_config() {
  local vm_name="$1"
  local port="$2"
  local user="$3"

  if [[ -z "$vm_name" || -z "$port" || -z "$user" ]]; then
    echo "Usage: add_ssh_config <vm_name> <port> <user>" >&2
    return 1
  fi

  local ssh_config="$HOME/.ssh/config"

  mkdir -p "$HOME/.ssh"
  touch "$ssh_config"

  if grep -qE "^Host[[:space:]]+$vm_name\$" "$ssh_config"; then
    echo "Host $vm_name already exists in SSH config" >&2
    return 0
  fi

  {
    echo "Host $vm_name"
    echo "    HostName 127.0.0.1"
    echo "    User $user"
    echo "    Port $port"
    echo "    IdentityFile ~/.ssh/id_rsa"
    echo "    StrictHostKeyChecking no"
    echo "    UserKnownHostsFile /dev/null"
    echo
  } >>"$ssh_config"

  echo "Added SSH config for $vm_name (port $port, user $user)"
}

add_port_command() {
  local ssh_config="$HOME/.ssh/config"
  touch "$ssh_config"

  if grep -qE "^Host[[:space:]]+\\*-vmu$" "$ssh_config"; then
    return
  fi

  cat <<'EOF' >>"$ssh_config"
Host *-vmu
  HostName 127.0.0.1
  ProxyCommand sh -c 'port=$(grep -m1 "^%h " ~/.vm-ports | awk "{print \$2}"); exec nc 127.0.0.1 $port'
  StrictHostKeyChecking no
  UserKnownHostsFile=/dev/null
  LogLevel ERROR
EOF
}

# wait_for_ssh() {
#   local vm_name=$1
#   local max_attempts=60 # 5 minutes max
#   local attempt=0

#   echo "Waiting for VM to be ready and SSH accessible..."

#   while [ $attempt -lt $max_attempts ]; do
#     # First check if VM is running
#     if [ "$(virsh --connect qemu:///session domstate "$vm_name")" = "running" ]; then
#       # Try to add port forwarding (idempotent)
#       virsh --connect qemu:///session qemu-monitor-command "$vm_name" \
#         --hmp "hostfwd_add ::2222-:22" 2>/dev/null || true

#       # Test if SSH port is responding
#       if timeout 5 bash -c "</dev/tcp/localhost/2222" 2>/dev/null; then
#         echo "SSH is ready!"
#         return 0
#       fi
#     fi

#     echo "Attempt $((attempt + 1))/$max_attempts - VM not ready yet..."
#     sleep 5
#     ((attempt++))
#   done

#   echo "Timeout waiting for VM to be ready"
#   return 1
# }

# check_and_add_port_forward() {
#   local vm_name=$1
#   local host_port=${2:-2222}
#   local guest_port=${3:-22}

#   echo "Checking existing port forwards for VM: $vm_name"

#   # Get current network info
#   network_info=$(virsh --connect qemu:///session qemu-monitor-command "$vm_name" \
#     --hmp "info network" 2>/dev/null || echo "")

#   # Check if our port forwarding already exists
#   if echo "$network_info" | grep -q "TCP\[HOST_FORWARD\].*:${host_port}-"; then
#     echo "Port forwarding already exists for port $host_port"
#     return 0
#   fi

#   echo "Adding port forwarding: localhost:$host_port -> guest:$guest_port"
#   if virsh --connect qemu:///session qemu-monitor-command "$vm_name" \
#     --hmp "hostfwd_add ::${host_port}-:${guest_port}"; then
#     echo "Port forwarding added successfully"
#     return 0
#   else
#     echo "Failed to add port forwarding"
#     return 1
#   fi
# }

LIBVIRT_ISO_POOL_PATH="/srv/libvirt/images/iso"
LIBVIRT_ISO_POOL_NAME="iso-pool"

libvirt_iso_pool_create() {
  if virsh pool-info "$LIBVIRT_ISO_POOL_NAME" >/dev/null 2>&1; then
    return 0
  fi

  echo "Creating ISO pool $LIBVIRT_ISO_POOL_NAME at $LIBVIRT_ISO_POOL_PATH..."
  sudo mkdir -p "$LIBVIRT_ISO_POOL_PATH"
  sudo virsh pool-define-as "$LIBVIRT_ISO_POOL_NAME" dir --target "$LIBVIRT_ISO_POOL_PATH"
  sudo virsh pool-start "$LIBVIRT_ISO_POOL_NAME"
  sudo virsh pool-autostart "$LIBVIRT_ISO_POOL_NAME"
}
