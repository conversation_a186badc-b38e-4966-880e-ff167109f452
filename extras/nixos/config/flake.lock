{"nodes": {"agenix": {"inputs": {"darwin": "darwin", "home-manager": "home-manager", "nixpkgs": "nixpkgs", "systems": "systems"}, "locked": {"lastModified": 1754433428, "narHash": "sha256-NA/FT2hVhKDftbHSwVnoRTFhes62+7dxZbxj5Gxvghs=", "owner": "ryantm", "repo": "agenix", "rev": "9edb1787864c4f59ae5074ad498b6272b3ec308d", "type": "github"}, "original": {"owner": "ryantm", "repo": "agenix", "type": "github"}}, "base16": {"inputs": {"fromYaml": "fromYaml"}, "locked": {"lastModified": 1746562888, "narHash": "sha256-YgNJQyB5dQiwavdDFBMNKk1wyS77AtdgDk/VtU6wEaI=", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "base16.nix", "rev": "806a1777a5db2a1ef9d5d6f493ef2381047f2b89", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "base16.nix", "type": "github"}}, "base16-fish": {"flake": false, "locked": {"lastModified": 1622559957, "narHash": "sha256-PebymhVYbL8trDVVXxCvZgc0S5VxI7I1Hv4RMSquTpA=", "owner": "<PERSON><PERSON><PERSON>", "repo": "base16-fish", "rev": "2f6dd973a9075dabccd26f1cded09508180bf5fe", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "repo": "base16-fish", "type": "github"}}, "base16-helix": {"flake": false, "locked": {"lastModified": 1752979451, "narHash": "sha256-0CQM+FkYy0fOO/sMGhOoNL80ftsAzYCg9VhIrodqusM=", "owner": "tinted-theming", "repo": "base16-helix", "rev": "27cf1e66e50abc622fb76a3019012dc07c678fac", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "base16-helix", "type": "github"}}, "base16-vim": {"flake": false, "locked": {"lastModified": 1732806396, "narHash": "sha256-e0bpPySdJf0F68Ndanwm+KWHgQiZ0s7liLhvJSWDNsA=", "owner": "tinted-theming", "repo": "base16-vim", "rev": "577fe8125d74ff456cf942c733a85d769afe58b7", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "base16-vim", "rev": "577fe8125d74ff456cf942c733a85d769afe58b7", "type": "github"}}, "darwin": {"inputs": {"nixpkgs": ["agenix", "nixpkgs"]}, "locked": {"lastModified": 1744478979, "narHash": "sha256-dyN+teG9G82G+m+PX/aSAagkC+vUv0SgUw3XkPhQodQ=", "owner": "lnl7", "repo": "nix-darwin", "rev": "43975d782b418ebf4969e9ccba82466728c2851b", "type": "github"}, "original": {"owner": "lnl7", "ref": "master", "repo": "nix-darwin", "type": "github"}}, "disko": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1755519972, "narHash": "sha256-bU4nqi3IpsUZJeyS8Jk85ytlX61i4b0KCxXX9YcOgVc=", "owner": "nix-community", "repo": "disko", "rev": "4073ff2f481f9ef3501678ff479ed81402caae6d", "type": "github"}, "original": {"owner": "nix-community", "repo": "disko", "type": "github"}}, "firefox-gnome-theme": {"flake": false, "locked": {"lastModified": 1748383148, "narHash": "sha256-pGvD/RGuuPf/4oogsfeRaeMm6ipUIznI2QSILKjKzeA=", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repo": "firefox-gnome-theme", "rev": "4eb2714fbed2b80e234312611a947d6cb7d70caf", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repo": "firefox-gnome-theme", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": "nixpkgs-lib"}, "locked": {"lastModified": 1754487366, "narHash": "sha256-pHYj8gUBapuUzKV/kN/tR3Zvqc7o6gdFB9XKXIp1SQ8=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "af66ad14b28a127c5c0f3bbb298218fc63528a18", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-parts_2": {"inputs": {"nixpkgs-lib": ["nixvim", "nixpkgs"]}, "locked": {"lastModified": 1754487366, "narHash": "sha256-pHYj8gUBapuUzKV/kN/tR3Zvqc7o6gdFB9XKXIp1SQ8=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "af66ad14b28a127c5c0f3bbb298218fc63528a18", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-parts_3": {"inputs": {"nixpkgs-lib": ["stylix", "nixpkgs"]}, "locked": {"lastModified": 1751413152, "narHash": "sha256-Tyw1RjYEsp5scoigs1384gIg6e0GoBVjms4aXFfRssQ=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "77826244401ea9de6e3bac47c2db46005e1f30b5", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems_2"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils_2": {"locked": {"lastModified": 1667395993, "narHash": "sha256-nuEHfE/LcWyuSWnS8t12N1wc105Qtau+/OdUAjtQ0rA=", "owner": "numtide", "repo": "flake-utils", "rev": "5aed5285a952e0b949eb3ba02c12fa4fcfef535f", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "fromYaml": {"flake": false, "locked": {"lastModified": 1731966426, "narHash": "sha256-lq95WydhbUTWig/JpqiB7oViTcHFP8Lv41IGtayokA8=", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "fromYaml", "rev": "106af9e2f715e2d828df706c386a685698f3223b", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "fromYaml", "type": "github"}}, "gnome-shell": {"flake": false, "locked": {"lastModified": 1748186689, "narHash": "sha256-UaD7Y9f8iuLBMGHXeJlRu6U1Ggw5B9JnkFs3enZlap0=", "owner": "GNOME", "repo": "gnome-shell", "rev": "8c88f917db0f1f0d80fa55206c863d3746fa18d0", "type": "github"}, "original": {"owner": "GNOME", "ref": "48.2", "repo": "gnome-shell", "type": "github"}}, "home-manager": {"inputs": {"nixpkgs": ["agenix", "nixpkgs"]}, "locked": {"lastModified": 1745494811, "narHash": "sha256-YZCh2o9Ua1n9uCvrvi5pRxtuVNml8X2a03qIFfRKpFs=", "owner": "nix-community", "repo": "home-manager", "rev": "abfad3d2958c9e6300a883bd443512c55dfeb1be", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "home-manager_2": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1755625756, "narHash": "sha256-t57ayMEdV9g1aCfHzoQjHj1Fh3LDeyblceADm2hsLHM=", "owner": "nix-community", "repo": "home-manager", "rev": "dd026d86420781e84d0732f2fa28e1c051117b59", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "ixx": {"inputs": {"flake-utils": ["nixvim", "nuschtosSearch", "flake-utils"], "nixpkgs": ["nixvim", "nuschtosSearch", "nixpkgs"]}, "locked": {"lastModified": 1748294338, "narHash": "sha256-FVO01jdmUNArzBS7NmaktLdGA5qA3lUMJ4B7a05Iynw=", "owner": "NuschtOS", "repo": "ixx", "rev": "cc5f390f7caf265461d4aab37e98d2292ebbdb85", "type": "github"}, "original": {"owner": "NuschtOS", "ref": "v0.0.8", "repo": "ixx", "type": "github"}}, "nixlib": {"locked": {"lastModified": 1736643958, "narHash": "sha256-tmpqTSWVRJVhpvfSN9KXBvKEXplrwKnSZNAoNPf/S/s=", "owner": "nix-community", "repo": "nixpkgs.lib", "rev": "1418bc28a52126761c02dd3d89b2d8ca0f521181", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixpkgs.lib", "type": "github"}}, "nixos-generators": {"inputs": {"nixlib": "nixlib", "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1751903740, "narHash": "sha256-PeSkNMvkpEvts+9DjFiop1iT2JuBpyknmBUs0Un0a4I=", "owner": "nix-community", "repo": "nixos-generators", "rev": "032decf9db65efed428afd2fa39d80f7089085eb", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixos-generators", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1754028485, "narHash": "sha256-IiiXB3BDTi6UqzAZcf2S797hWEPCRZOwyNThJIYhUfk=", "owner": "NixOS", "repo": "nixpkgs", "rev": "59e69648d345d6e8fef86158c555730fa12af9de", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-25.05", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-lib": {"locked": {"lastModified": 1753579242, "narHash": "sha256-zvaMGVn14/Zz8hnp4VWT9xVnhc8vuL3TStRqwk22biA=", "owner": "nix-community", "repo": "nixpkgs.lib", "rev": "0f36c44e01a6129be94e3ade315a5883f0228a6e", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixpkgs.lib", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1755615617, "narHash": "sha256-HMwfAJBdrr8wXAkbGhtcby1zGFvs+StOp19xNsbqdOg=", "owner": "NixOS", "repo": "nixpkgs", "rev": "20075955deac2583bb12f07151c2df830ef346b4", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixvim": {"inputs": {"flake-parts": "flake-parts_2", "nixpkgs": ["nixpkgs"], "nuschtosSearch": "nuschtosSearch", "systems": "systems_3"}, "locked": {"lastModified": 1755717891, "narHash": "sha256-MbuYOji6oxqk2nawrfjnKkAoXnVqrXAp1vQPdjtb/Q4=", "owner": "nix-community", "repo": "nixvim", "rev": "1bd91097c381aafec012babcfcd1d90821a0782e", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixvim", "type": "github"}}, "nur": {"inputs": {"flake-parts": ["stylix", "flake-parts"], "nixpkgs": ["stylix", "nixpkgs"]}, "locked": {"lastModified": 1751906969, "narHash": "sha256-BSQAOdPnzdpOuCdAGSJmefSDlqmStFNScEnrWzSqKPw=", "owner": "nix-community", "repo": "NUR", "rev": "ddb679f4131e819efe3bbc6457ba19d7ad116f25", "type": "github"}, "original": {"owner": "nix-community", "repo": "NUR", "type": "github"}}, "nuschtosSearch": {"inputs": {"flake-utils": "flake-utils", "ixx": "ixx", "nixpkgs": ["nixvim", "nixpkgs"]}, "locked": {"lastModified": 1754301638, "narHash": "sha256-aRgzcPDd2axHFOuMlPLuzmDptUM2JU8mUL3jfgbBeyc=", "owner": "NuschtOS", "repo": "search", "rev": "a60091045273484c040a91f5c229ba298f8ecc27", "type": "github"}, "original": {"owner": "NuschtOS", "repo": "search", "type": "github"}}, "quadlet-nix": {"locked": {"lastModified": 1754008153, "narHash": "sha256-MYT1mDtSkiVg343agxgBFsnuNU3xS8vRy399JXX1Vw0=", "owner": "SEIAROTg", "repo": "quadlet-nix", "rev": "1b2d27d460d8c7e4da5ba44ede463b427160b5c4", "type": "github"}, "original": {"owner": "SEIAROTg", "repo": "quadlet-nix", "type": "github"}}, "root": {"inputs": {"agenix": "agenix", "disko": "disko", "flake-parts": "flake-parts", "home-manager": "home-manager_2", "nixos-generators": "nixos-generators", "nixpkgs": "nixpkgs_2", "nixvim": "nixvim", "quadlet-nix": "quadlet-nix", "stylix": "stylix", "xc": "xc"}}, "stylix": {"inputs": {"base16": "base16", "base16-fish": "base16-fish", "base16-helix": "base16-helix", "base16-vim": "base16-vim", "firefox-gnome-theme": "firefox-gnome-theme", "flake-parts": "flake-parts_3", "gnome-shell": "gnome-shell", "nixpkgs": ["nixpkgs"], "nur": "nur", "systems": "systems_4", "tinted-foot": "tinted-foot", "tinted-kitty": "tinted-kitty", "tinted-schemes": "tinted-schemes", "tinted-tmux": "tinted-tmux", "tinted-zed": "tinted-zed"}, "locked": {"lastModified": 1755708361, "narHash": "sha256-RmqBx2EamhIk0WVhQSNb8iehaVhilO7D0YAnMoFPqJQ=", "owner": "danth", "repo": "stylix", "rev": "2355da455d7188228aaf20ac16ea9386e5aa6f0c", "type": "github"}, "original": {"owner": "danth", "repo": "stylix", "type": "github"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_2": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_3": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_4": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "tinted-foot": {"flake": false, "locked": {"lastModified": 1726913040, "narHash": "sha256-+eDZPkw7efMNUf3/Pv0EmsidqdwNJ1TaOum6k7lngDQ=", "owner": "tinted-theming", "repo": "tinted-foot", "rev": "fd1b924b6c45c3e4465e8a849e67ea82933fcbe4", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "tinted-foot", "rev": "fd1b924b6c45c3e4465e8a849e67ea82933fcbe4", "type": "github"}}, "tinted-kitty": {"flake": false, "locked": {"lastModified": 1735730497, "narHash": "sha256-4KtB+FiUzIeK/4aHCKce3V9HwRvYaxX+F1edUrfgzb8=", "owner": "tinted-theming", "repo": "tinted-kitty", "rev": "de6f888497f2c6b2279361bfc790f164bfd0f3fa", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "tinted-kitty", "type": "github"}}, "tinted-schemes": {"flake": false, "locked": {"lastModified": 1750770351, "narHash": "sha256-LI+BnRoFNRa2ffbe3dcuIRYAUcGklBx0+EcFxlHj0SY=", "owner": "tinted-theming", "repo": "schemes", "rev": "5a775c6ffd6e6125947b393872cde95867d85a2a", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "schemes", "type": "github"}}, "tinted-tmux": {"flake": false, "locked": {"lastModified": 1751159871, "narHash": "sha256-UOHBN1fgHIEzvPmdNMHaDvdRMgLmEJh2hNmDrp3d3LE=", "owner": "tinted-theming", "repo": "tinted-tmux", "rev": "bded5e24407cec9d01bd47a317d15b9223a1546c", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "tinted-tmux", "type": "github"}}, "tinted-zed": {"flake": false, "locked": {"lastModified": 1751158968, "narHash": "sha256-ksOyv7D3SRRtebpXxgpG4TK8gZSKFc4TIZpR+C98jX8=", "owner": "tinted-theming", "repo": "base16-zed", "rev": "86a470d94204f7652b906ab0d378e4231a5b3384", "type": "github"}, "original": {"owner": "tinted-theming", "repo": "base16-zed", "type": "github"}}, "xc": {"inputs": {"flake-utils": "flake-utils_2", "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1752144863, "narHash": "sha256-hqfOLUyr4C4HwaHrShXk+pTH/VqbPLqWFJ1K7Ofp+Ug=", "owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "xc", "rev": "a1fec24f33cba46ce9552b23518f02fdaa03d28f", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON><PERSON>", "repo": "xc", "type": "github"}}}, "root": "root", "version": 7}