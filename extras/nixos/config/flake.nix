{
  description = "ILM NixOS configuration";
  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    agenix.url = "github:ryantm/agenix";
    quadlet-nix.url = "github:SEIAROTg/quadlet-nix";
    flake-parts.url = "github:hercules-ci/flake-parts";
    disko = {
      url = "github:nix-community/disko";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    stylix = {
      url = "github:danth/stylix";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    home-manager = {
      url = "github:nix-community/home-manager";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    nixvim = {
      url = "github:nix-community/nixvim";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    nixos-generators = {
      url = "github:nix-community/nixos-generators";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    xc = {
      url = "github:joerdav/xc";
      inputs.nixpkgs.follows = "nixpkgs";
    };
  };

  outputs =
    {
      nixpkgs,
      agenix,
      stylix,
      home-manager,
      quadlet-nix,
      disko,
      nixos-generators,
      # nixvim,
      ...
    }@inputs:
    let
      system = "x86_64-linux";
      pkgs = import nixpkgs { inherit system; };
      vars = import ./vars.nix { inherit pkgs; };

      commonModules = [
        ./system/configuration.nix
        stylix.nixosModules.stylix
        quadlet-nix.nixosModules.quadlet
        # nixvim.nixosModules.nixvim
        home-manager.nixosModules.home-manager
        {
          home-manager = {
            extraSpecialArgs = {
              inherit inputs;
              inherit vars;
            };

            useUserPackages = true;
            useGlobalPkgs = true;
            backupFileExtension = "backup";
            users.${vars.userName} = import ./home/<USER>
          };
        }
      ];

      uiModules = commonModules ++ [
        ./system/ui.nix
      ];

      mkNixosSystem =
        modules:
        nixpkgs.lib.nixosSystem {
          inherit system;
          specialArgs = {
            inherit inputs;
            inherit vars;
          };
          modules = modules;
        };

      mkUiSystem = extraModules: mkNixosSystem (uiModules ++ extraModules);

      mkBareSystem =
        extraModules:
        mkNixosSystem (
          uiModules
          ++ extraModules
          ++ [
            agenix.nixosModules.default
          ]
        );

      mkVmSystem =
        extraModules:
        mkNixosSystem (
          uiModules
          ++ extraModules
          ++ [
            ./system/guest.nix
            ./system/ssh.nix
          ]
        );

      mkNixosGenerateCommon =
        extraModules: format:
        nixos-generators.nixosGenerate {
          inherit system;
          specialArgs = {
            pkgs = nixpkgs.legacyPackages.${system};
            inherit inputs;
            inherit vars;
          };
          modules = extraModules ++ [
            { virtualisation.diskImage.size = "20G"; }
            ./system/guest.nix
            ./system/ssh.nix
          ];
          format = format;
        };

      mkNixosGenerateVm = extraModules: mkNixosGenerateCommon extraModules "vm";

      mkNixosGenerateProxmox =
        extraModules: mkNixosGenerateCommon extraModules ++ [ ./system/proxmox.nix ] "proxmox";

      mkAnywhereSystem =
        extraModules:
        mkBareSystem (
          extraModules
          ++ [
            disko.nixosModules.disko
          ]
        );
    in
    {
      homeConfigurations = {
        ${vars.userName} = home-manager.lib.homeManagerConfiguration {
          pkgs = nixpkgs.legacyPackages.${system};
          extraSpecialArgs = { inherit inputs vars; };
          modules = [
            # nixvim.homeManagerModules.nixvim
            ./home/<USER>
          ];
        };
      };

      nixosConfigurations = {
        server = mkNixosSystem (commonModules ++ [ ./system/ssh.nix ]);

        gnome = mkUiSystem [ ./system/gnome.nix ];
        gnome-vm = mkVmSystem [ ./system/gnome.nix ];

        kde = mkUiSystem [ ./system/kde.nix ];
        kde-vm = mkVmSystem [ ./system/kde.nix ];

        sway = mkUiSystem [ ./system/sway.nix ];
        sway-vm = mkVmSystem [ ./system/sway.nix ];

        anywhere."${vars.hostName}" = mkAnywhereSystem [
          ./system/disko-config.nix
          ./system/gnome.nix
          ./system/ssh.nix
          ./hosts/${vars.hostName}/hardware-configuration.nix
        ];

        um580 = mkBareSystem [
          ./hosts/um580/hardware-configuration.nix
          ./hosts/um580/fs.nix
          ./system/sway.nix
        ];

        docker-vm = mkVmSystem [ ./system/virt/docker.nix ];

        # run with nix build .#ng-vm
        ng-vm = mkNixosGenerateVm [ ];

        # run with nix build .#ng-pmox
        ng-pmox = mkNixosGenerateProxmox [ ];

        "7945hx" = mkBareSystem [
          ./hosts/7945hx/hardware-configuration.nix
          ./system/gnome.nix
          ./system/apps.nix
          ./system/vm-ui.nix
        ];
      };
    };
}
